{"name": "CAT-Digital-Twin", "version": "1.0.0", "description": "Digital Twin Experince for CAT Models", "homepage": ".", "scripts": {"aqserver": "node nodeserver/server.cjs", "aq-with-client": "concurrently \"npm run aqserver\" \"npm run watch\"", "build-dev": "NODE_OPTIONS=--max-old-space-size=2048 webpack", "build": "NODE_OPTIONS=--max-old-space-size=2048 webpack --config webpack.prod.js", "watch": "webpack serve --open / --port 8084 --config webpack.config.js", "start": "NODE_OPTIONS=--max-old-space-size=2048 webpack serve --progress", "test-prod": "NODE_OPTIONS=--max-old-space-size=2048 webpack serve --config webpack.prod.js --port 8083"}, "keywords": [], "author": "Yash Team", "dependencies": {"@dtplatform/core-utils": "4.3.12", "@dtplatform/iaf-doc-viewer": "4.3.38", "@dtplatform/iaf-script-engine": "4.3.49", "@dtplatform/iaf-viewer": "4.3.61", "@dtplatform/invicara-lib": "4.3.4", "@dtplatform/platform-api": "4.3.37", "@dtplatform/react-ifef": "^4.6.0", "@dtplatform/ui-utils": "4.3.21", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@invicara/ipa-core": "3.0.21", "@lottiefiles/lottie-player": "^2.0.12", "@material-ui/core": "^4.8.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.36", "@mui/material": "^5.17.1", "@mui/styles": "^6.4.11", "@mui/x-charts": "^6.18.2", "@mui/x-data-grid": "^7.28.3", "@nivo/bar": "~0.84.0", "@nivo/line": "~0.84.0", "@nivo/pie": "~0.84.0", "@paciolan/remote-component": "^2.13.0", "@reduxjs/toolkit": "^1.3.6", "@tanstack/react-table": "^8.17.3", "assert": "^2.1.0", "bootstrap": "^4.3.1", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "canvas": "^2.11.2", "chart.js": "^4.4.8", "classnames": "^2.2.6", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.0", "events": "^3.3.0", "express": "^5.1.0", "file-saver": "^2.0.2", "https-browserify": "^1.0.0", "immer": "^6.0.9", "interactjs": "^1.9.20", "json-schema-faker": "^0.5.0-rcv.24", "jszip": "^3.4.0", "lodash": "^4.17.5", "mime-types": "^2.1.27", "moment": "^2.24.0", "mqtt": "^5.12.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "prop-types": "^15.6.0", "qs": "^6.9.3", "querystring-es3": "^0.2.1", "react": "^17.0.2", "react-activation": "^0.12.4", "react-autosuggest": "^9.4.3", "react-beautiful-dnd": "^13.0.0", "react-bootstrap": "^2.10.9", "react-click-outside": "^3.0.1", "react-color": "^2.19.3", "react-data-table-component": "^7.6.2", "react-date-picker": "^7.10.0", "react-datetime-picker": "^2.9.0", "react-dom": "^17.0.2", "react-dropzone": "^7.0.1", "react-hook-form": "^7.55.0", "react-inspector": "^6.0.2", "react-redux": "^7.2.0", "react-router": "^4.3.1", "react-router-dom": "^5.3.4", "react-select": "^3.0.8", "react-table": "^7.8.0", "react-transition-group": "^2.2.1", "recharts": "^2.15.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.3", "util": "^0.12.5", "vm-browserify": "^1.1.2"}, "devDependencies": {"@babel/cli": "^7.1.2", "@babel/core": "^7.1.2", "@babel/helpers": "^7.1.2", "@babel/plugin-external-helpers": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-regenerator": "^7.0.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.0.0", "babel-loader": "^8.0.4", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.1.2", "copy-webpack-plugin": "^6.0.0", "css-loader": "^0.28.4", "dotenv-webpack": "^8.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "fast-async": "^7.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "less": "^3.8.1", "less-loader": "^12.2.0", "regenerator-runtime": "^0.12.1", "sass": "1.77.6", "sass-loader": "14.2.1", "style-loader": "^0.20.2", "terser-webpack-plugin": "^4.2.3", "url-loader": "^0.5.9", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.1", "webpack-glsl-loader": "^1.0.1"}, "overrides": {"react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^5.3.4"}, "type": "module", "dev_twinit": {"createdBy": "create-twinit-app@3.0.8"}}